#include "UdpServerHandler.h"
#include "driversdk/cvdrivercommon.h"
#include "ace/Time_Value.h"
#include <ace/OS_NS_strings.h>
#include "processdb/DriverApi.h"
#include <ace/ACE.h>
#include "gettext/libintl.h"
#include "common/CommHelper.h"
#include "ace/Message_Block.h"

//#define _(STRING) gettext(STRING)
#define _(STRING) STRING
#define RECV_BUFFER_LEN 1024
extern string	g_strDrvName;
extern CCVLog g_CVDrvierCommonLog;
extern CSimpleThreadQueue<ACE_Message_Block*> g_devStatusQueue;
extern void proto_devstatus_pack(const char* szDevName, const TCV_TimeStamp* pCVTime, long nDrvStatus, char** ppBuf, int32* pnLen);


CUdpServerHandler::CUdpServerHandler(void)
{
	m_ulDevicePort = 0;
	m_uLocalPort = 0;
}


CUdpServerHandler::~CUdpServerHandler(void)
{
	DisConnect();
}

//格式：localport=xx;ip=*.*.*.*:port
long CUdpServerHandler::SetConnectParam( char *szConnParam )
{
	if (NULL == szConnParam)
		return -1;

	string strConnParam = szConnParam;
	int nPos = strConnParam.find("localport=");
	if(nPos == string::npos)
	{
        //设备 %s 作为UDPServer进行连接, 找不到localport参数
		CV_ERROR(g_CVDrvierCommonLog, -1, _("It Found an error when device %s connected as UDPServer, can not find localport parameter."), m_strDeviceName.c_str());
		return -1;
	}
    
	string strPortNo = strConnParam.substr(nPos + strlen("localport="));
	nPos = strPortNo.find(';');
	if(nPos != strPortNo.npos)
		strPortNo = strPortNo.substr(0, nPos);

	if(strPortNo.empty())
	{
        //设备 %s 作为UDPServer进行连接, 找不到localport=后面的端口号参数
		CV_ERROR(g_CVDrvierCommonLog, -2, _("It Found an error when device %s connected as UDPServer, can not find localport numner parameter."), m_strDeviceName.c_str());
		return -2;
	}

	m_uLocalPort = ::atoi(strPortNo.c_str());

	// 合法参数ip=xx.xx.xx.xx:port
	nPos = strConnParam.find("ip=");
	if(nPos != string::npos)
	{
		strConnParam = strConnParam.substr(nPos + strlen("ip="));
		string strDevicesIP = strConnParam;
		int nPosEnd = strDevicesIP.find(';'); 
		if(nPosEnd != string::npos)
		{
			m_strDeviceIP = strDevicesIP.substr(0, nPosEnd);
			if ((nPos = m_strDeviceIP.find(':')) != string::npos)
			{
				strPortNo = m_strDeviceIP.substr(nPos + 1);
				m_strDeviceIP = m_strDeviceIP.substr(0, nPos);
				m_ulDevicePort = atoi(strPortNo.c_str()); 
			}
		}
	}

	m_remoteAddr.set(m_ulDevicePort, m_strDeviceIP.c_str());

	return DRV_SUCCESS;
}

long CUdpServerHandler::Connect()
{
	ACE_INET_Addr port_to_listen(m_uLocalPort);    
	m_dgramSock.open(port_to_listen);
	std::string strSubKeyPrefix = g_strDrvName;
	strSubKeyPrefix += "#";
	strSubKeyPrefix += m_strDeviceName;
	// drv_redis_batchbegin();
	// drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_NAME, m_strDeviceName.c_str());
	// drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_ADDR, m_strDeviceIP.c_str());
	// drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_PORT, m_ulDevicePort);
	//drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_CONSTATUS, (long)DRV_SUCCESS);
	//drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_CONDEVICE, pDevice->m_nCurDeviceType);
	// drv_redis_batchaddtime(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_STATUS);
	// drv_redis_batchsubmit();
	return DRV_SUCCESS;
}

//FIXME:删掉的头文件里的宏定义，后面看看放在哪合适

//驱动状态队列最大值
// #define DRV_DEV_SATATUS_QUEUE_MAX  10000

long CUdpServerHandler::DisConnect()
{
	m_dgramSock.close();
	 if (g_devStatusQueue.size()< 10000)
	 {
		 //更新设备状态
		 TCV_TimeStamp cvTimeStamp = (timeval)ACE_OS::gettimeofday();
		 char* pBuf = NULL;
		 int32 nLenBuf = 0;
		 proto_devstatus_pack(m_strDeviceName.c_str(), &cvTimeStamp, DEV_STATUS_BAD, &pBuf, &nLenBuf);
		 ACE_Message_Block*  pMsg = new ACE_Message_Block(nLenBuf);
		 pMsg->copy(pBuf, nLenBuf);
		 SAFE_DELETE_ARRAY(pBuf);
		 g_devStatusQueue.enqueue(pMsg);
	 }

	return DRV_SUCCESS;
}

long CUdpServerHandler::Send( const char* pszSendBuf, long nSendBytes, long lTimeoutMs )
{
	ACE_Time_Value tvTimeout;
	tvTimeout.msec(lTimeoutMs);

	return m_dgramSock.send(pszSendBuf, nSendBytes, m_remoteAddr, 0, &tvTimeout);
}

long CUdpServerHandler::Recv( char* szRecvBuf, long nRecvBytes, long lTimeoutMs )
{
	ACE_Time_Value tvTimeout;
	ACE_INET_Addr remoteAddr;
	tvTimeout.msec(lTimeoutMs);
	long lRecvBytes = m_dgramSock.recv(szRecvBuf, nRecvBytes, remoteAddr, 0, &tvTimeout);
	char szIpAddr[RECV_BUFFER_LEN] = {'\0'};
	remoteAddr.addr_to_string(szIpAddr, RECV_BUFFER_LEN);
	//recv from the unknown device
	if (lRecvBytes >0 && !m_strDeviceIP.empty() && strstr(szIpAddr, m_strDeviceIP.c_str()) == NULL)
		return -1;
	return lRecvBytes;
}

long CUdpServerHandler::Recv_n( char *szReadBuf, long lExactReadBufLen, long lTimeOutMS )
{
	ACE_Time_Value tvTimeout;
	ACE_INET_Addr remoteAddr;
	tvTimeout.msec(lTimeOutMS);
	return m_dgramSock.recv(szReadBuf, lExactReadBufLen, remoteAddr, 0, &tvTimeout);
}

void CUdpServerHandler::ClearDeviceRecvBuffer()
{
	char szBuffer[RECV_BUFFER_LEN] = {'\0'};

	int nRecvBytes = 0;
	while((nRecvBytes = Recv(szBuffer, sizeof(szBuffer), 2000)) > 0)
	{
		if (nRecvBytes < sizeof(szBuffer))
			break;
	}
}
long CUdpServerHandler::Start()
{
	return Connect();
}

long CUdpServerHandler::Stop()
{
	return DisConnect();
} 

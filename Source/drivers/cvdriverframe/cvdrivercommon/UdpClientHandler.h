#ifndef UDP_CLIENT_HANDLER_H_
#define UDP_CLIENT_HANDLER_H_

#include <ace/SOCK_Dgram.h>
#include <common/CVLog.h>
#include <common/cvdefine.h>
#include "common/SimpleQueue.h"
#include "DeviceConnection.h"
#include <string>
using namespace std;

#define DEFAULT_INPUT_SIZE		65535	// 接收缓存区默认大小

class CUdpClientHandler : public CDeviceConnection
{
public:
	CUdpClientHandler(void);
	~CUdpClientHandler(void);
	virtual long Start();
	virtual long Stop();
	virtual long SetConnectParam(char *szConnParam);
	// 连接设备
	virtual long Connect();
	// 断开连接
	virtual long DisConnect();
	// 发送数据
	virtual long Send(const char* pszSendBuf, long nSendBytes, long lTimeoutMs);
	virtual long Recv(char* szRecvBuf, long nSendBytes, long lTimeoutMs);
	virtual long Recv_n(char *szReadBuf, long lExactReadBufLen, long lTimeOutMS);

	virtual void ClearDeviceRecvBuffer();
protected:
	string	m_strDeviceName;
	ACE_SOCK_Dgram m_dgramSock;
    char *m_szRecvBuf;
	unsigned short m_port;		// 监听端口
};
#endif
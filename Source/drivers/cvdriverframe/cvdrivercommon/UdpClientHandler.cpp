#include "UdpClientHandler.h"
#include "driversdk/cvdrivercommon.h"
#include "ace/Time_Value.h"
#include <ace/OS_NS_strings.h>
#include "processdb/DriverApi.h"
#include <ace/ACE.h>
#include "gettext/libintl.h"
#include "common/CommHelper.h"
#include "ace/Message_Block.h"

//#define _(STRING) gettext(STRING)
#define _(STRING) STRING

extern string	g_strDrvName;
extern CCVLog g_CVDrvierCommonLog;
extern CSimpleThreadQueue<ACE_Message_Block*> g_devStatusQueue;
extern void proto_devstatus_pack(const char* szDevName, const TCV_TimeStamp* pCVTime, long nDrvStatus, char** ppBuf, int32* pnLen);

CUdpClientHandler::CUdpClientHandler(void)
{
    m_szRecvBuf = new char[DEFAULT_INPUT_SIZE];

	m_port = 0;
}

CUdpClientHandler::~CUdpClientHandler(void)
{
	DisConnect();

    if(m_szRecvBuf)
		delete [] m_szRecvBuf;
	m_szRecvBuf = NULL;
}

long CUdpClientHandler::SetConnectParam(char *szConnParam)
{
	if (NULL == szConnParam)
		return -1;

	string strConnParam = szConnParam;
	int nPos = strConnParam.find("port=");
	if(nPos == string::npos)
	{
        //设备 %s 作为UDPClinet进行连接, 找不到port参数
		CV_ERROR(g_CVDrvierCommonLog, -1, _("It Found an error when device %s connected as UDPClient, can not find port parameter."), m_strDeviceName.c_str());
		return -1;
	}
    
	string strPortNo = strConnParam.substr(nPos + strlen("port="));
	nPos = strPortNo.find(';');
	if(nPos != strPortNo.npos)
		strPortNo = strPortNo.substr(0, nPos);

	if(strPortNo.empty())
	{
        //设备 %s 作为UDPClient进行连接, 找不到port=后面的端口号参数
		CV_ERROR(g_CVDrvierCommonLog, -2, _("It Found an error when device %s connected as UDPClient, can not find port numner parameter."), m_strDeviceName.c_str());
		return -2;
	}

	m_port = ::atoi(strPortNo.c_str());

	return DRV_SUCCESS;
}

long CUdpClientHandler::Connect()
{

	return DRV_SUCCESS;
}

long CUdpClientHandler::DisConnect()
{
	return DRV_SUCCESS;
}

long CUdpClientHandler::Send( const char* pszSendBuf, long nSendBytes, long lTimeoutMs )
{
	return DRV_SUCCESS;
}

long CUdpClientHandler::Recv( char* szRecvBuf, long nRecvBytes, long lTimeoutMs )
{
	
}

long CUdpClientHandler::Recv_n( char *szReadBuf, long lExactReadBufLen, long lTimeOutMS )
{
	// UDP是无连接协议，Recv_n与Recv行为相同
	return Recv(szReadBuf, lExactReadBufLen, lTimeOutMS);
}

void CUdpClientHandler::ClearDeviceRecvBuffer()
{

}

long CUdpClientHandler::Start()
{
	return Connect();
}

long CUdpClientHandler::Stop()
{
	return DisConnect();
}
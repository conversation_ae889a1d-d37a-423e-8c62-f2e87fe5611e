/**************************************************************
 *  Filename:    SockHandler.cpp
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: SockHandler.cpp
 *
 *  @author:     lijingjing
 *  @version     05/29/2008  lijingjing  Initial Version
 *  @version     06/01/2011  chenzhiquan  将回调函数独立到单独现场处理
 *                                        避免死锁
**************************************************************/

#include <ace/ACE.h>
#include "TcpClientHandler.h"
#include "common/CVLog.h"
#include "common/cvGlobalHelper.h"
#include <ace/OS_NS_sys_socket.h>
#include <ace/SOCK_SEQPACK_Association.h>
#include <string>
#include "driversdk/cvdrivercommon.h"
#include "common/LogHelper.inl"
#include <ace/OS_NS_strings.h>
#include "processdb/DriverApi.h"
#include "gettext/libintl.h"
#include "common/CommHelper.h"

#ifdef __sun
#include <sys/socket.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#endif


 //FIXME:删掉的头文件里的宏定义，后面看看放在哪合适

#define DRV_DEV_SATATUS_QUEUE_MAX 1000

//#define _(STRING) gettext(STRING)
#define _(STRING) STRING

extern string	g_strDrvName;
extern CCVLog g_CVDrvierCommonLog;
extern CSimpleThreadQueue<ACE_Message_Block*> g_devStatusQueue;
extern bool IsActiveHost();
extern void proto_devstatus_pack(const char* szDevName, const TCV_TimeStamp* pCVTime, long nDrvStatus, char** ppBuf, int32* pnLen);
/**
 *  构造函数.
 *
 *  @param  -[in]  ACE_Reactor*  r: [comment]
 *
 *  @version     05/29/2008  lijingjing  Initial Version.
 */
CTcpClientHandler::CTcpClientHandler() 
{
	// 为每个设备开一个线程
	m_szRecvBuf = new char[DEFAULT_INPUT_SIZE];

	m_strCurIPAddress = "";
	m_nPort = 0;
	m_nPort2 = 0;
	m_nCurPort = 0;
	m_pfnRecvCallback = NULL;
	m_pfnConnectCallback = NULL;
	m_pCallbackParam = NULL;
	m_lCallbackParam = 0;
	memset(m_szCallbackParam, 0, sizeof(m_szCallbackParam));

	m_bConnected = false;
	m_bEnableConnect = true;
	m_bPrimaryDevActive = true;
	m_nConnTimeOut = 3;	// 3秒缺省
	m_tmLastConnect = 0;
	m_bMultiLink = false;				// 是否支持多连接
	m_usLocalPort = 0;
}

/**
 *  析构函数.
 *
 *  @version     05/29/2008  lijingjing  Initial Version.
 */
CTcpClientHandler::~CTcpClientHandler()
{
	DisConnect();

	if(m_szRecvBuf)
		delete [] m_szRecvBuf;
	m_szRecvBuf = NULL;
}

// 连接参数，如果是tcp则包括:IP,Port,可选的连接超时。以后考虑可以有串口
// 这里一定是tcpclient连接方式。形式: IP:***********;Port:502;ConnTimeOut:3000
long CTcpClientHandler::SetConnectParam(char *szConnParam)
{
	string strConnParam = szConnParam;
	CV_INFO(g_CVDrvierCommonLog, _("Set connection parameter %s"), szConnParam);
	strConnParam += ";"; // 补上一个分号
	int nPos = strConnParam.find(';');
	while(nPos != string::npos)
	{
		string strOneParam = strConnParam.substr(0, nPos);
		strConnParam = strConnParam.substr(nPos + 1);// 除去这一个已经解析过的参数
		nPos = strConnParam.find(';');

		if(strOneParam.empty())
			continue;

		int nPosPart = strOneParam.find('=');	// IP:**********
		if(nPosPart == string::npos)
			continue;

		// 获取到某个参数名称和值
		string strParamName = strOneParam.substr(0, nPosPart); // e.g. IP
		string strParamValue = strOneParam.substr(nPosPart + 1); // e.g. **********

		if(ACE_OS::strcasecmp("ip", strParamName.c_str()) == 0)
			m_strMainIPAddress = strParamValue;
		if(ACE_OS::strcasecmp("ip2", strParamName.c_str()) == 0)
			m_strBakeIPAddress = strParamValue;
		else if(ACE_OS::strcasecmp("port", strParamName.c_str()) == 0)
			m_nPort = ::atoi(strParamValue.c_str());
		else if(ACE_OS::strcasecmp("port2", strParamName.c_str()) == 0)
			m_nPort2 = ::atoi(strParamValue.c_str());
		else if(ACE_OS::strcasecmp("conntimeout", strParamName.c_str()) == 0)
			m_nConnTimeOut = ::atoi(strParamValue.c_str());
		else if(ACE_OS::strcasecmp("multilink", strParamName.c_str()) == 0)
			m_bMultiLink = ::atoi(strParamValue.c_str());
		else if (ACE_OS::strcasecmp("localport", strParamName.c_str()) == 0)
			m_usLocalPort = ::atoi(strParamValue.c_str());
		else
			;
	}

	m_strCurIPAddress = m_strMainIPAddress;
	m_nCurPort = m_nPort;
	m_bPrimaryDevActive = true;

	m_tmLastConnect = 0;	// 使得可以立即进行连接

	return DRV_SUCCESS;
}

/**
 *  连接设备.
 *
 *
 *  @version     07/01/2008  lijingjing  Initial Version.
 *  @version	8/13/2013  baoyuansong  增加对连接服务器时指定本地端口的支持.
 *  @version	9/2/2013  baoyuansong  修改本地端口重用.
 */
 long CTcpClientHandler::Connect()
{
	// 连接已经建立
	if (m_bConnected)
		return DRV_SUCCESS;

	// 不允许重连，用于单连接设备且是备机情况下
	if(!m_bMultiLink && !IsActiveHost())
	{
		// ACE_Time_Value timeValue = ACE_OS::gettimeofday();
		// TCV_TimeStamp timeStamp;
		// timeStamp.tv_sec = (uint32_t)timeValue.sec();
		// timeStamp.tv_usec = (uint32_t)timeValue.usec();
		// char szTime[ICV_HOSTNAMESTRING_MAXLEN] = {'\0'};
		// cvcommon::CastTimeToASCII(szTime, ICV_HOSTNAMESTRING_MAXLEN, timeStamp);

		// char szKey[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szKey, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szKey, sizeof(szKey), "driver#%s#%s#status", g_strDrvName.c_str(), m_strDeviceName.c_str());
		// char szStatus[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szStatus, sizeof(szStatus), "%d;%s", CV_STATUS_DEVICE_CONNFAIL, szTime);
		// STATERW_SetStringCommand(&g_pStateRW, szKey, szStatus);
		return -100;
	}

	// 小于2倍最小连接超时则不能重连
	time_t tmNow;
	time(&tmNow);
	int nTimeSpan = (int)abs(tmNow - m_tmLastConnect);
	if(nTimeSpan < m_nConnTimeOut * 2)
		return - 1;

	if (0 != m_tmLastConnect)
		SwitchServAddr();

	time(&m_tmLastConnect);

	ACE_INET_Addr destAddr(m_nCurPort, m_strCurIPAddress.c_str());
	// 确保后面能进行了连接
	close();
	set_handle(ACE_INVALID_HANDLE);	

	CONNECTOR connector(reactor());

	// 设置默认连接超时
	ACE_Time_Value timeout(m_nConnTimeOut);
	ACE_Synch_Options synch_option(ACE_Synch_Options::USE_TIMEOUT, timeout);
	
	// 设置为同步方式，以便为后面的连接做准备
	peer().disable(ACE_NONBLOCK);

	// 与服务器建立连接
	// 连接状态的改变OnConnectStateChange是在事件回调中处理的
	CTcpClientHandler *pSockHandler = this;

	int nRet = 0;
	if (m_usLocalPort >0)
	{
		ACE_INET_Addr localAddr(m_usLocalPort);
		nRet = connector.connect(pSockHandler, destAddr, synch_option, localAddr, 1);
	}
	else
		nRet = connector.connect(pSockHandler, destAddr, synch_option);

	std::string strSubKeyPrefix = g_strDrvName;
	strSubKeyPrefix += "#";
	strSubKeyPrefix += m_strDeviceName;
	// drv_redis_batchbegin();
	// drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_NAME, m_strDeviceName.c_str());
	// drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_ADDR, m_strCurIPAddress.c_str());
	// drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_PORT, m_nCurPort);
	//drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_CONSTATUS, nRet);
	// drv_redis_batchaddtime(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_STATUS);
	// drv_redis_batchsubmit();
	if(nRet == -1)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("Connect to device %s (%s: %d) failed!"), m_strDeviceName.c_str(), m_strCurIPAddress.c_str(), m_nCurPort);//连接设备(%s: %d)失败!
		return -1;
	}

	//获取本地IP和端口信息
	ACE_INET_Addr localInfo;
	size_t addr_size = 1;
	ACE_SOCK_SEQPACK_Association ssa(pSockHandler->get_handle());
	ssa.get_local_addrs(&localInfo, addr_size); // 获取连接本地的IP和端口
	char szLocalAddr[64];
	memset(szLocalAddr, 0 , sizeof(szLocalAddr));
	localInfo.addr_to_string(szLocalAddr, 63); // 格式为IP:Port

	CV_INFO(g_CVDrvierCommonLog, _("Connect to device%s (%s: %d) successfully! local addr: %s (config port: %d)"), m_strDeviceName.c_str(), m_strCurIPAddress.c_str(), m_nCurPort, szLocalAddr, m_usLocalPort);//连接设备(%s: %d)成功!
	
#ifdef __sun
	//由于solaris实测下来，网络中断后的5分钟内非阻塞send一直返回正确的发送字节数而非-1，需要通过TCP协议的keepalive机制来缩短检测到网络中断的时间 by wangyadong 20141229
	int nKeepAlive = 1;
	int nTCPKeepaliveThreshold = 10000;
	int nTCPKeepaliveAbortThreshold = 1000;
	int nTCPRTOMax = 1000;
	int nTCPAbortThreshold = 3000;
	nRet = this->peer().set_option(SOL_SOCKET, SO_KEEPALIVE, &nKeepAlive, sizeof(nKeepAlive));
	nRet = this->peer().set_option(IPPROTO_TCP, TCP_KEEPALIVE_THRESHOLD, &nTCPKeepaliveThreshold, sizeof(nTCPKeepaliveThreshold));
	nRet = this->peer().set_option(IPPROTO_TCP, TCP_KEEPALIVE_ABORT_THRESHOLD , &nTCPKeepaliveAbortThreshold, sizeof(nTCPKeepaliveAbortThreshold));
	nRet = this->peer().set_option(IPPROTO_TCP, TCP_RTO_MAX, &nTCPRTOMax, sizeof(nTCPRTOMax));
	nRet = this->peer().set_option(IPPROTO_TCP, TCP_ABORT_THRESHOLD, &nTCPAbortThreshold, sizeof(nTCPAbortThreshold));
#endif
	return DRV_SUCCESS;
}

 /**
 *  断开连接.
 *
 *
 *  @version     07/01/2008  lijingjing  Initial Version.
 */
 long CTcpClientHandler::DisConnect()
{
	if (m_bConnected)	// 处于连接状态
	{
		// 关闭连接。似乎不用调，close里面调过了
		ACE_OS::shutdown(get_handle(), ACE_SHUTDOWN_BOTH);
		peer().close();
		set_handle(ACE_INVALID_HANDLE);
		//int nRet = ACE_OS::closesocket(m_sockHandler.get_handle()); 
		OnConnectStateChange(false);
		CV_INFO(g_CVDrvierCommonLog, "Disconnect with %s(%s:%d)", this->m_strDeviceName.c_str(),  m_strCurIPAddress.c_str(), m_nCurPort);
	}

	return DRV_SUCCESS;
}

 void CTcpClientHandler::CheckConnect()
 {
	 if(!m_bConnected)
		 Connect();
 }

 void CTcpClientHandler::OnConnectStateChange(bool bConnected)
 {
	 m_bConnected = bConnected;

	 //更新设备状态
	 if (g_devStatusQueue.size()< DRV_DEV_SATATUS_QUEUE_MAX)
	 {
		 TCV_TimeStamp cvTimeStamp = (timeval)ACE_OS::gettimeofday();
		 char* pBuf = NULL;
		 int32 nLenBuf = 0;
		 proto_devstatus_pack(m_strDeviceName.c_str(), &cvTimeStamp, m_bConnected, &pBuf, &nLenBuf);
		 ACE_Message_Block*  pMsg = new ACE_Message_Block(nLenBuf);
		 pMsg->copy(pBuf, nLenBuf);
		 SAFE_DELETE_ARRAY(pBuf);
		 g_devStatusQueue.enqueue(pMsg);
	 }

	 if(m_pfnConnectCallback)
		 m_pfnConnectCallback(bConnected, m_lCallbackParam, m_pCallbackParam, m_szCallbackParam);
 }
/**
 *  建立连接时被调用.
 *
 *  @param  -[in]  void*  p: [comment]
 *
 *  @version     05/29/2008  lijingjing  Initial Version.
 */
int CTcpClientHandler::open(void *p)
{
	// 设置后面的接收和发送为异步方式
	if(this->peer().enable(ACE_NONBLOCK) == -1)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1,  _("Establish connection with %s, set asynchronization mode failed, exit"), 
			this->m_strDeviceName.c_str());
		return -1;
	}

	if (super::open (p) == -1)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("Initiate connection successfully，but open(p) failed"));
		return -1;
	}
	
	ACE_INET_Addr deviceAddr;
	if (this->peer().get_remote_addr (deviceAddr) != 0)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("Received connection request from device, but failed to get remote address, and then disconnected with device"));
		return -1;
	}

	// 获取对端的IP和Port信息
	string strDeviceIP = deviceAddr.get_host_addr();
	unsigned short uDevicePort = deviceAddr.get_port_number();
	CV_INFO(g_CVDrvierCommonLog, _("Connected to %s(%s:%d)"), this->m_strDeviceName.c_str(),  strDeviceIP.c_str(), uDevicePort);

	m_bConnected = true;
	 if (g_devStatusQueue.size()< DRV_DEV_SATATUS_QUEUE_MAX)
	 {
		 //更新设备状态
		 TCV_TimeStamp cvTimeStamp = (timeval)ACE_OS::gettimeofday();
		 char* pBuf = NULL;
		 int32 nLenBuf = 0;
		 proto_devstatus_pack(m_strDeviceName.c_str(), &cvTimeStamp, DEV_STATUS_GOOD, &pBuf, &nLenBuf);
		 ACE_Message_Block*  pMsg = new ACE_Message_Block(nLenBuf);
		 pMsg->copy(pBuf, nLenBuf);
		 SAFE_DELETE_ARRAY(pBuf);
		 g_devStatusQueue.enqueue(pMsg);
	 }

	if(m_pfnConnectCallback)
		m_pfnConnectCallback(true, m_lCallbackParam, m_pCallbackParam, m_szCallbackParam);
	return 0;
}

/**
 *  当有输入时该函数被调用.
 *
 *  @param  -[in]    ACE_HANDLE: [comment]
 *
 *  @version     05/29/2008  lijingjing  Initial Version.
 *  @version     09/25/2010  chenzhiquan 接收到文件后输出日志
 */
int CTcpClientHandler::handle_input(ACE_HANDLE)
{	
	if(m_pfnRecvCallback == NULL)//huangsongxin 回调函数目前没用。如果继续接收数据会影响到正常的收发
	{
		return DRV_SUCCESS;
	}
	// 接收数据
	this->peer().enable(ACE_NONBLOCK);  
	ssize_t nRecvBytes = this->peer().recv(m_szRecvBuf, DEFAULT_INPUT_SIZE);

	if(nRecvBytes == 0) //connection loss  
		return -1;
	else if (nRecvBytes == -1) // block  
	{  
		if (errno == EWOULDBLOCK)  
			return 0;  
		else   
			return -1;  
	}  
	while(nRecvBytes > 0)
	{
		if(m_pfnRecvCallback)
			m_pfnRecvCallback(m_szRecvBuf, nRecvBytes, m_lCallbackParam, m_pCallbackParam, m_szCallbackParam);

		// 可能还有数据未完全接收下来，继续接收
		if(nRecvBytes != DEFAULT_INPUT_SIZE)
			break;

		// 可能还有数据未完全接收下来，继续接收
		// if(nRecvBytes == DEFAULT_INPUT_SIZE)
		nRecvBytes = this->peer().recv(m_szRecvBuf, DEFAULT_INPUT_SIZE);
		if(nRecvBytes == 0) //connection loss  
			return -1;
		else if (nRecvBytes == -1) // block  
		{  
			if (errno == EWOULDBLOCK)  
				return 0;  
			else   
				return -1;  
		}
	}

	return DRV_SUCCESS;
}

/**
 *  当有输出时该函数被调用.
 *
 *  @param  -[in]  ACE_HANDLE  fd: [comment]
 *
 *  @version     05/29/2008  lijingjing  Initial Version.
 */
int CTcpClientHandler::handle_output (ACE_HANDLE fd /* = ACE_INVALID_HANDLE */)
{
	
	return 0;
}

/**
 *  当SockHandler从ACE_Reactor中移除时该函数被调用.
 *
 *  @param  -[in]  ACE_HANDLE  handle: [comment]
 *  @param  -[in]  ACE_Reactor_Mask  close_mask: [comment]
 *
 *  @version     05/29/2008  lijingjing  Initial Version.
 */
int CTcpClientHandler::handle_close(ACE_HANDLE handle, ACE_Reactor_Mask close_mask)
{	
	ACE_TRACE((LM_TRACE, ACE_TEXT("SockHandler::handle_close()\n")));
	this->reactor()->remove_handler
		(this->get_handle (),
		// Remove all the events for which we’re
		// registered. We must pass the DONT_CALL
		// flag here to avoid infinite recursion.
		ACE_Event_Handler::ALL_EVENTS_MASK |
		ACE_Event_Handler::DONT_CALL);

	// 设置为同步方式，以便为后面的连接做准备
	this->peer().disable(ACE_NONBLOCK);

	OnConnectStateChange(false);
	ACE_OS::shutdown(this->get_handle(), 0x02);
	ACE_OS::closesocket(this->get_handle());
	set_handle(ACE_INVALID_HANDLE);
	CV_INFO(g_CVDrvierCommonLog, "Disconnected with %s(%s:%d)", this->m_strDeviceName.c_str(),  m_strCurIPAddress.c_str(), m_nCurPort);

	return 0;
}

/**
 *  发送数据.
 *
 *  @param  -[in]  char*  pszSendBuf: [comment]
 *  @param  -[in]  int  nSendBytes: [comment]
 *
 *  @version     05/30/2008  lijingjing  Initial Version.
 */
long CTcpClientHandler::Send(const char* pszSendBuf, long nSendBytes, long lTimeoutMs)
{
	ACE_Time_Value timeValue = ACE_OS::gettimeofday();
	TCV_TimeStamp timeStamp;
	timeStamp.tv_sec = (uint32_t)timeValue.sec();
	timeStamp.tv_usec = (uint32_t)timeValue.usec();
	char szTime[ICV_HOSTNAMESTRING_MAXLEN] = {'\0'};
	cvcommon::CastTimeToASCII(szTime, ICV_HOSTNAMESTRING_MAXLEN, timeStamp);

	// 如果没有连接则尝试重连
	if(!m_bConnected)
	{
		// 不允许重连，用于单连接设备且是备机情况下
		if(!m_bMultiLink && !IsActiveHost())
		{
			// char szKey[ICV_HOSTNAMESTRING_MAXLEN];
			// memset(szKey, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
			// ACE_OS::snprintf(szKey, sizeof(szKey), "driver#%s#%s#status", g_strDrvName.c_str(), m_strDeviceName.c_str());
			// char szStatus[ICV_HOSTNAMESTRING_MAXLEN];
			// memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
			// ACE_OS::snprintf(szStatus, sizeof(szStatus), "%d;%s", CV_STATUS_DEVICE_CONNFAIL, szTime);
			// STATERW_SetStringCommand(&g_pStateRW, szKey, szStatus);
			return -100;
		}
	}

	// 多连接设备或者是主机情况下
	if(!m_bConnected)
		Connect();

	// 主备都连接不上
	if(!m_bConnected)
	{
		// char szKey[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szKey, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szKey, sizeof(szKey), "driver#%s#%s#status", g_strDrvName.c_str(), m_strDeviceName.c_str());
		// char szStatus[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szStatus, sizeof(szStatus), "%d;%s", CV_STATUS_DEVICE_CONNFAIL, szTime);
		// STATERW_SetStringCommand(&g_pStateRW, szKey, szStatus);
		return -101;
	}

	// 发送数据
	ssize_t nSent = 0;
	//if(nAsyncMode)
	//	nSent = this->peer().send(pszSendBuf, nSendBytes, 0);
	//else
	{
		ACE_Time_Value tvTimeout(0, 1000 * lTimeoutMs);

		nSent = this->peer().send_n(pszSendBuf, nSendBytes, &tvTimeout); // 会阻塞在这里，因此采用下面的非阻塞调用
		CV_DEBUG(g_CVDrvierCommonLog, _("-----------send_n return value %d, errno %d"), nSent, errno);
	}

	if (nSent == -1)
	{
		// char szKey[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szKey, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szKey, sizeof(szKey), "driver#%s#%s#status", g_strDrvName.c_str(), m_strDeviceName.c_str());
		// char szStatus[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szStatus, sizeof(szStatus), "%d;%s", CV_STATUS_DEVICE_CONNFAIL, szTime);
		// STATERW_SetStringCommand(&g_pStateRW, szKey, szStatus);

		if(errno != EWOULDBLOCK)
		{
			// 发生了未知异常（如机器休眠时），此时发送总是返回-1，需要断开重连
			if(this->get_handle() != 0) 
			{
				ACE_OS::shutdown(this->get_handle(), 0x02);
				ACE_OS::closesocket(this->get_handle());
				set_handle(ACE_INVALID_HANDLE);
				m_bConnected = false;

				//更新设备状态
				 if (g_devStatusQueue.size()< DRV_DEV_SATATUS_QUEUE_MAX)
				{
					TCV_TimeStamp cvTimeStamp = (timeval)ACE_OS::gettimeofday();
					char* pBuf = NULL;
					int32 nLenBuf = 0;
					proto_devstatus_pack(m_strDeviceName.c_str(), &cvTimeStamp, DEV_STATUS_BAD, &pBuf, &nLenBuf);
					ACE_Message_Block*  pMsg = new ACE_Message_Block(nLenBuf);
					pMsg->copy(pBuf, nLenBuf);
					SAFE_DELETE_ARRAY(pBuf);
					g_devStatusQueue.enqueue(pMsg);
				}

				CV_ERROR(g_CVDrvierCommonLog, -1, "%s(%s:%d)connection closed, errno =  %d.", m_strDeviceName.c_str(), m_strCurIPAddress.c_str(), m_nCurPort, errno);
			}
			return nSent;
		}
		else
			return nSent;
	}

	if (nSent != nSendBytes)		// 发送失败,由于是非阻塞调用，所以不能关闭socket
	{
		if(this->get_handle() != 0) 
		{
			// ACE_OS::shutdown(this->get_handle(), 0x02);
			// ACE_OS::closesocket(this->get_handle());
		}

		// char szKey[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szKey, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szKey, sizeof(szKey), "driver#%s#%s#status", g_strDrvName.c_str(), m_strDeviceName.c_str());
		// char szStatus[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szStatus, sizeof(szStatus), "%d;%s", CV_STATUS_DEVICE_CONNFAIL, szTime);
		// STATERW_SetStringCommand(&g_pStateRW, szKey, szStatus);
		return nSent; // EC_ICV_DA_IO_SEND_ERROR;
	}
	
// 	char szKey[ICV_HOSTNAMESTRING_MAXLEN];
// 	memset(szKey, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
// 	ACE_OS::snprintf(szKey, sizeof(szKey), "driver#%s#%s#status", g_strDrvName.c_str(), m_strDeviceName.c_str());
// 	char szStatus[ICV_HOSTNAMESTRING_MAXLEN];
// 	memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
// 	ACE_OS::snprintf(szStatus, sizeof(szStatus), "0;%s", szTime);
// 	CV_DEBUG(g_CVDrvierCommonLog, _("-----------send_n STATERW_SetStringCommand szKey :%s"), szKey);
// 
// 	STATERW_SetStringCommand(&g_pStateRW, szKey, szStatus);
// 	CV_DEBUG(g_CVDrvierCommonLog, _("-----------send_n STATERW_SetStringCommand szStatus :%s"), szStatus);

	//modify by hsx

	return nSent;
}


/**
 *  发送数据.
 *
 *  @param  -[in]  char*  pszSendBuf: [comment]
 *  @param  -[in]  int  nSendBytes: [comment]
 *
 *  @version     05/30/2008  lijingjing  Initial Version.
 */
long CTcpClientHandler::Recv(char* szRecvBuf, long nSendBytes, long lTimeoutMs)
{
	CheckConnect();

	// 如果没有连接则尝试重连
	if(!m_bConnected)
	{
		// 不允许重连，用于单连接设备且是备机情况下
		if(!m_bEnableConnect)
			return -1;

		Connect();
		// 尝试重连后仍然没有能连上
		if(!m_bConnected)
			return -1;
	}

	ACE_Time_Value tvTimeout(0, 1000 * lTimeoutMs);
	// 接收数据
	ssize_t nSent = 0;
	nSent = this->peer().recv((void *)szRecvBuf, nSendBytes, &tvTimeout);
	return nSent;
}

/**
 *  发送数据.
 *
 *  @param  -[in]  char*  pszSendBuf: [comment]
 *  @param  -[in]  int  nSendBytes: [comment]
 *
 *  @version     05/30/2008  lijingjing  Initial Version.
 */
long CTcpClientHandler::Recv_n(char* szRecvBuf, long nSendBytes, long lTimeoutMs)
{
	CheckConnect();

	// 如果没有连接则尝试重连
	if(!m_bConnected)
	{
		// 不允许重连，用于单连接设备且是备机情况下
		if(!m_bEnableConnect)
			return -1;

		Connect();
		// 尝试重连后仍然没有能连上
		if(!m_bConnected)
			return -1;
	}

	ACE_Time_Value tvTimeout(0, 1000 * lTimeoutMs);
	// 发送数据
	ssize_t nSent = 0;
	nSent = this->peer().recv_n((void *)szRecvBuf, nSendBytes, &tvTimeout);
	return nSent;
}

void CTcpClientHandler::ClearDeviceRecvBuffer()
{
	char szBuffer[2048];

    int nRecvBytes = 0;
    while((nRecvBytes = Recv(szBuffer, sizeof(szBuffer), 0)) > 0)
	{
        if (nRecvBytes < sizeof(szBuffer))
            break;
	}
}

long CTcpClientHandler::Start()
{
	return Connect();
}

long CTcpClientHandler::Stop()
{
	return DisConnect();
}

void CTcpClientHandler::SwitchServAddr()
{
	if(!m_strBakeIPAddress.empty() && m_strBakeIPAddress.compare(m_strMainIPAddress) != 0)//备机IP不为空并且与主机IP不同
	{
		if(m_bPrimaryDevActive)
		{
			m_strCurIPAddress = m_strBakeIPAddress;
			if (m_nPort2 != 0)
				m_nCurPort = m_nPort2;
			else
				m_nCurPort = m_nPort;
		}
		else
		{
			m_strCurIPAddress = m_strMainIPAddress;
			m_nCurPort = m_nPort;
		}

		std::string strSubKeyPrefix = g_strDrvName;
		strSubKeyPrefix += "#";
		strSubKeyPrefix += m_strDeviceName;
		// drv_redis_time_t(strSubKeyPrefix.c_str(), DRV_REDIS_TIME_DEVSWITCH);
		m_bPrimaryDevActive = !m_bPrimaryDevActive;
	}
}


long CTcpClientHandler::GetConnectedState()
{
	if(m_strCurIPAddress.compare(m_strMainIPAddress) == 0 )
		return MAINISCONNECTED;
	else if(m_strCurIPAddress.compare(m_strBakeIPAddress) == 0)
		return BACKISCONNECTED;
	else
		return 0;
}

void CTcpClientHandler::SetRecvCallBackFunc(PFN_CVCommRecvCallBack pfnRecvFunc, void* pCallbackParam, long lCallbackParam, char *pszCallbackParam)
{
	m_pfnRecvCallback = pfnRecvFunc;
	m_pCallbackParam = pCallbackParam;
	m_lCallbackParam = lCallbackParam;
	memcpy(m_szCallbackParam, pszCallbackParam, ICV_DEVICENAME_MAXLEN);
}
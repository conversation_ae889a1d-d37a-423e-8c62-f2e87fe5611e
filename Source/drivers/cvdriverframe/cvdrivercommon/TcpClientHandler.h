/**************************************************************
*  Filename:    SockHandler.h
*  Copyright:   Shanghai Baosight Software Co., Ltd.
*
*  Description: SockHandler.h.
*
*  @author:     lijingjing
*  @version     05/29/2008  lijingjing  Initial Version
**************************************************************/

#ifndef _SOCK_HANDLER_H_
#define _SOCK_HANDLER_H_

#include <ace/Svc_Handler.h>
#include <ace/Connector.h>
#include <ace/SOCK_Connector.h>
#include <common/CVLog.h>
#include <common/cvdefine.h>
#include "common/SimpleQueue.h"
#include "DeviceConnection.h"
#include <string>

using namespace std;

#define DEFAULT_INPUT_SIZE		66000	// 接收缓存区默认大小

typedef void (*PFN_CVCommConnCallBack)(int nConnected, long lParam, void* pCallbackParam, char *szPararm);
typedef void (*PFN_CVCommRecvCallBack)(char* pszRecvBuf, int nRecvBytes, long lParam, void* pCallbackParam, char *szPararm);

class CTcpClientHandler : public ACE_Svc_Handler<ACE_SOCK_STREAM, ACE_MT_SYNCH>, public CDeviceConnection
{
public:
	
	typedef ACE_Svc_Handler<ACE_SOCK_STREAM, ACE_MT_SYNCH> super;
	
	CTcpClientHandler() ;
	virtual ~CTcpClientHandler();
	
public:
	virtual long Start();
	virtual long Stop();
	// 连接参数，如果是tcp则包括:IP,Port,可选的连接超时。以后考虑可以有串口
	// 这里一定是tcpclient连接方式。形式: IP:***********;Port:502;ConnTimeOut:3000
	virtual long SetConnectParam(char *szConnParam);
	// 连接设备
	virtual long Connect();
	// 断开连接
	virtual long DisConnect();

	void SwitchServAddr();
	// 发送数据
	virtual long Send(const char* pszSendBuf, long nSendBytes, long lTimeoutMs);
	virtual long Recv(char* szRecvBuf, long nSendBytes, long lTimeoutMs);
	virtual long Recv_n(char* szRecvBuf, long nSendBytes, long lTimeoutMs);
	virtual bool IsConnected(){return m_bConnected;}
	virtual void ClearDeviceRecvBuffer();

	//获取连接的是主还是备设备，1是主，2是备
	long GetConnectedState();
public:
	bool	m_bPrimaryDevActive;		// 主机是活动机器
	bool	m_bMultiLink;				// 是否支持多连接
protected:
	void OnConnectStateChange(bool bConnected);
	// 检查是否要断开重连
	void CheckConnect();

public:
	virtual int open(void* p = 0);	
	// 当有输入时该函数被调用.
	virtual int handle_input(ACE_HANDLE fd = ACE_INVALID_HANDLE);
	 
	// 当有输出时该函数被调用.
	virtual int handle_output(ACE_HANDLE fd = ACE_INVALID_HANDLE);
	
	// 当SockHandler从ACE_Reactor中移除时该函数被调用.
	virtual int handle_close(ACE_HANDLE handle, ACE_Reactor_Mask close_mask);
public:
	string	m_strDeviceName; 
protected:
	char *	m_szRecvBuf;
	bool	m_bEnableConnect;			// 是否允许连接和尝试重连。用于单连接设备情况
	string	m_strCurIPAddress;				// 服务器IP地址
	string	m_strMainIPAddress;			// 备份服务器IP地址
	string	m_strBakeIPAddress;			// 备份服务器IP地址
	unsigned short m_nPort;					// 服务器端口号
	unsigned short m_nPort2;			// 服务器备机端口号
	unsigned short m_nCurPort;
	unsigned short m_usLocalPort;  
	bool	m_bConnected;
	PFN_CVCommRecvCallBack m_pfnRecvCallback;		// 接收到数据时的回调函数
	PFN_CVCommConnCallBack m_pfnConnectCallback;		// 连接回调函数

protected:
	time_t	m_tmLastConnect;
	long	m_lCallbackParam;				// 回调函数的第1个参数
	char	m_szCallbackParam[ICV_DEVICENAME_MAXLEN];	// 回调函数的第2个参数
	void*	m_pCallbackParam;				// 回调函数的第3个参数
	int		m_nConnTimeOut;	//连接超时

public:
	void SetRecvCallBackFunc(PFN_CVCommRecvCallBack pfnRecvFunc, void* pCallbackParam, long lCallbackParam, char *pszCallbackParam);
};

typedef ACE_Connector<CTcpClientHandler, ACE_SOCK_CONNECTOR>  CONNECTOR;

#endif // _SOCK_HANDLER_H_

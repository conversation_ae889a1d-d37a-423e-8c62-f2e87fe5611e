/**************************************************************
*  Filename:    SockHandler.h
*  Copyright:   Shanghai Baosight Software Co., Ltd.
*
*  Description: SockHandler.h.
*
*  @author:     lijingjing
*  @version     05/29/2008  lijingjing  Initial Version
**************************************************************/

#ifndef _SOCK_HANDLER_H_
#define _SOCK_HANDLER_H_

#include <ace/Svc_Handler.h>
#include <ace/Connector.h>
#include <ace/SOCK_Connector.h>
#include <common/CVLog.h>
#include <common/cvdefine.h>
#include "common/SimpleQueue.h"
#include "DeviceConnection.h"
#include <string>

using namespace std;

#define DEFAULT_INPUT_SIZE		66000	// ���ջ�����Ĭ�ϴ�С

typedef void (*PFN_CVCommConnCallBack)(int nConnected, long lParam, void* pCallbackParam, char *szPararm);
typedef void (*PFN_CVCommRecvCallBack)(char* pszRecvBuf, int nRecvBytes, long lParam, void* pCallbackParam, char *szPararm);

class CTcpClientHandler : public ACE_Svc_Handler<ACE_SOCK_STREAM, ACE_MT_SYNCH>, public CDeviceConnection
{
public:
	
	typedef ACE_Svc_Handler<ACE_SOCK_STREAM, ACE_MT_SYNCH> super;
	
	CTcpClientHandler() ;
	virtual ~CTcpClientHandler();
	
public:
	virtual long Start();
	virtual long Stop();
	// ���Ӳ����������tcp�����:IP,Port,��ѡ�����ӳ�ʱ���Ժ��ǿ����д���
	// ����һ����tcpclient���ӷ�ʽ����ʽ: IP:***********;Port:502;ConnTimeOut:3000
	virtual long SetConnectParam(char *szConnParam);
	// �����豸
	virtual long Connect();
	// �Ͽ�����
	virtual long DisConnect();

	void SwitchServAddr();
	// ��������
	virtual long Send(const char* pszSendBuf, long nSendBytes, long lTimeoutMs);
	virtual long Recv(char* szRecvBuf, long nSendBytes, long lTimeoutMs);
	virtual long Recv_n(char* szRecvBuf, long nSendBytes, long lTimeoutMs);
	virtual bool IsConnected(){return m_bConnected;}
	virtual void ClearDeviceRecvBuffer();

	//��ȡ���ӵ��������Ǳ��豸��1������2�Ǳ�
	long GetConnectedState();
public:
	bool	m_bPrimaryDevActive;		// �����ǻ����
	bool	m_bMultiLink;				// �Ƿ�֧�ֶ�����
protected:
	void OnConnectStateChange(bool bConnected);
	// ����Ƿ�Ҫ�Ͽ�����
	void CheckConnect();

public:
	virtual int open(void* p = 0);	
	// ��������ʱ�ú���������.
	virtual int handle_input(ACE_HANDLE fd = ACE_INVALID_HANDLE);
	 
	// �������ʱ�ú���������.
	virtual int handle_output(ACE_HANDLE fd = ACE_INVALID_HANDLE);
	
	// ��SockHandler��ACE_Reactor���Ƴ�ʱ�ú���������.
	virtual int handle_close(ACE_HANDLE handle, ACE_Reactor_Mask close_mask);
public:
	string	m_strDeviceName; 
protected:
	char *	m_szRecvBuf;
	bool	m_bEnableConnect;			// �Ƿ��������Ӻͳ������������ڵ������豸���
	string	m_strCurIPAddress;				// ������IP��ַ
	string	m_strMainIPAddress;			// ���ݷ�����IP��ַ
	string	m_strBakeIPAddress;			// ���ݷ�����IP��ַ
	unsigned short m_nPort;					// �������˿ں�
	unsigned short m_nPort2;			// �����������˿ں�
	unsigned short m_nCurPort;
	unsigned short m_usLocalPort;  
	bool	m_bConnected;
	PFN_CVCommRecvCallBack m_pfnRecvCallback;		// ���յ�����ʱ�Ļص�����
	PFN_CVCommConnCallBack m_pfnConnectCallback;		// ���ӻص�����

protected:
	time_t	m_tmLastConnect;
	long	m_lCallbackParam;				// �ص������ĵ�1������
	char	m_szCallbackParam[ICV_DEVICENAME_MAXLEN];	// �ص������ĵ�2������
	void*	m_pCallbackParam;				// �ص������ĵ�3������
	int		m_nConnTimeOut;	//���ӳ�ʱ

public:
	void SetRecvCallBackFunc(PFN_CVCommRecvCallBack pfnRecvFunc, void* pCallbackParam, long lCallbackParam, char *pszCallbackParam);
};

typedef ACE_Connector<CTcpClientHandler, ACE_SOCK_CONNECTOR>  CONNECTOR;

#endif // _SOCK_HANDLER_H_

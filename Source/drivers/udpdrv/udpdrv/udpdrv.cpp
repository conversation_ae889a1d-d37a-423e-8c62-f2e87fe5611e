#include <stdlib.h>
#include <memory.h>
#include "ace/OS_NS_sys_time.h"
#include "udpdrv.h"
#include "math.h"

CCVLog g_CVLogUdp;
const char g_szDriverName[ICV_DRIVERNAME_MAXLEN] = "opcuadrv";

map<DRVHANDLE, map<DRVHANDLE, CVDATABLOCK*> > g_mapDataBlocks;

CVDRIVER_EXPORTS long Begin()
{
	g_CVLogUdp.SetLogFileNameThread(g_szDriverName);
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long Initialize(DRVHA<PERSON>LE hDriver)
{
	return 0;
}

CVDRIVER_EXPORTS long UnInitialize()
{
    g_CVLogUdp.StopLogThread();
    return DRV_SUCCESS;
}

void CheckBlockStatus(DRVHANDLE hDevice, DRVHANDLE hDatablock, long lSuccess)
{
	if(lSuccess == DRV_SUCCESS)
		Drv_SetUserData(hDatablock, 0, 0);
	else
	{
		long lFailCountRecent = Drv_GetUserData(hDatablock, 0);
		if(lFailCountRecent > 3)	// 最近失败次数
		{
			Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_COMM_FAILURE);
			Drv_SetUserData(hDatablock, 0, 0); // 避免计数太大导致循环
		}
		else
			Drv_SetUserData(hDatablock, 0, lFailCountRecent + 1);
	}
}

CVDRIVER_EXPORTS long OnDataBlockTimer(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
	//Drv_ClearRecvBuffer(hDevice);
	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);

	// 从设备接收消息
	long lCurRecvLen = 0;
	char szResponse[DEFAULT_RESPONSE_MAXLEN] = {0};

	//tdc协议7没有开始标记以及结束标记，由于电文在传输的过程中会拆包，所以必须校验是不是一个电文的第一个包
	//校验规则为必须存在准备接收这段电文数据的数据块，即存在配置有该电文号的数据块并且数据块长度与电文数据长度匹配
	unsigned short nPackageLen = 0;
	CV_TRACE(g_CVLogUdp, "Drv_RecvFromDevice Device %s  receive start ", pDevice->pszName);
	long lRecvBytes = Drv_RecvFromDevice(hDevice, szResponse + lCurRecvLen, sizeof(szResponse) - lCurRecvLen, pDevice->nRecvTimeout);
	CV_TRACE(g_CVLogUdp, "Drv_RecvFromDevice Device %s  receive end   lRecvBytes = %d ", pDevice->pszName, lRecvBytes);

	ACE_Time_Value* pLastRecvDataTime = (ACE_Time_Value*)Drv_GetUserDataPtr(hDevice, 1);
	ACE_Time_Value* pDisconnectTimeout = (ACE_Time_Value*)Drv_GetUserDataPtr(hDevice, 0);
	ACE_Time_Value tvCurrentTime = ACE_OS::gettimeofday();
	if(lRecvBytes <= 0)
	{
		//本次没有接收到数据，但是并没有到超时断开连接的时间
		if (tvCurrentTime - *pLastRecvDataTime < *pDisconnectTimeout)
		{
			return DRV_SUCCESS;
		}

		//接收超时，说明链路上一直没有数据，置该链路上的所有数据块质量为BAD
		for (std::map<DRVHANDLE, CVDATABLOCK*>::iterator itDB = g_mapDataBlocks[hDevice].begin(); itDB != g_mapDataBlocks[hDevice].end(); ++itDB)
		{
			Drv_UpdateBlockStatus(hDevice, itDB->first, DATA_STATUS_TIMEOUT_FAILURE);
		}

		CV_WARN(g_CVLogUdp, -1, "Disconnect with device %s because of receive timeout %d ms", pDevice->pszName, pDevice->nRecvTimeout);
		long lRet = Drv_DisconnectDevice(hDevice);
		CV_CHECK_FAIL_LOG(g_CVLogUdp, lRet, lRet, "Drv_DisconnectDevice");
		*pLastRecvDataTime = tvCurrentTime;
		Drv_SetUserDataPtr(hDevice, 1, pLastRecvDataTime);

		return DRV_SUCCESS;
	}
	else if (lRecvBytes <= PROTOCOL7_PACKAGE_HEADER_LEN)
	{
		//不合法的包长度
		CV_WARN(g_CVLogUdp, -1, "Device %s receive package of invalid size %d", pDevice->pszName, lRecvBytes);
		return DRV_SUCCESS;
	}

	CV_TRACE(g_CVLogUdp, "Drv_RecvFrom Device %s  receive %d bytes ", pDevice->pszName, lRecvBytes);
	*pLastRecvDataTime = tvCurrentTime;
	Drv_SetUserDataPtr(hDevice, 1, pLastRecvDataTime);
	//收到第一个包后解析出包长度字段以及电文号字段
	lCurRecvLen += lRecvBytes;
	unsigned short nTelID = 0;
	memcpy(&nPackageLen, szResponse, 2);
	memcpy(&nTelID, szResponse + 2, 2);
	//通过包长度字段和电文号寻找匹配的数据块

	DRVHANDLE hTargetDB = NULL;
	for (std::map<DRVHANDLE, CVDATABLOCK*>::iterator itDB = g_mapDataBlocks[hDevice].begin(); itDB != g_mapDataBlocks[hDevice].end(); ++itDB)
	{
		if (nTelID == atoi(itDB->second->pszParam1) && nPackageLen == itDB->second->nElemBits / BITS_PER_BYTE * itDB->second->nElemNum + 4)
		{
			//找到后如果还有后续包，继续收剩下的
			hTargetDB = itDB->first;
			while (lCurRecvLen < nPackageLen)
			{
				lRecvBytes = Drv_RecvFromDevice(hDevice, szResponse + lCurRecvLen, sizeof(szResponse) - lCurRecvLen, pDevice->nRecvTimeout);
				if (lRecvBytes <= 0)
				{
					Drv_UpdateBlockStatus(hDevice, itDB->first, DATA_STATUS_TIMEOUT_FAILURE);
					CV_WARN(g_CVLogUdp, -1, "Device %s datablock %s receive timeout %d ms", pDevice->pszName, itDB->second->pszName, pDevice->nRecvTimeout);
					return DRV_SUCCESS;
				}
				CV_TRACE(g_CVLogUdp, "Drv_RecvFrom Device %s  receive %d bytes total bytes is %d ", pDevice->pszName, lRecvBytes, lCurRecvLen);
				lCurRecvLen += lRecvBytes;
			}		
		}
	}

	if (hTargetDB)
	{
		//找到匹配的数据块，更新数据块
		CVDATABLOCK* pDatablock = Drv_GetDataBlockInfo(hTargetDB);
		int nSize = (pDatablock->nBlockDataSize > (nPackageLen - 4)) ? (nPackageLen - 4) : pDatablock->nBlockDataSize;
		Drv_LogMessage(DRV_LOGLEVEL_DEBUG, "Update device %s, datablock:%s, nSize:%d ", pDevice->pszName, pDatablock->pszName, nSize);
		Drv_UpdateBlockData(hDevice, hTargetDB, szResponse + 4, 0, nSize, DATA_STATUS_OK, NULL);
	}
	else
	{
		//没有找到和电文号对应的数据块，丢弃该电文
		CV_DEBUG(g_CVLogUdp, "Can't find the corresponding datablock[PackageLen=%d, TelID=%d]", nPackageLen, nTelID);
	}
	
	//本次读取完成，将剩余的数据清空
	long lRecvNoUseBytes = 0;
	if (lRecvBytes == DEFAULT_RESPONSE_MAXLEN)
	{
		do
		{
			memset(szResponse, 0x00, sizeof(szResponse));
			lRecvNoUseBytes = Drv_RecvFromDevice(hDevice, szResponse, sizeof(szResponse), 0);
			Drv_LogMessage(DRV_LOGLEVEL_DEBUG, "Remove device %s message ", pDevice->pszName);
		} while (lRecvNoUseBytes > 0);
	}

	return DRV_SUCCESS;
}

/* 不实现 */
CVDRIVER_EXPORTS long OnWriteCmd(DRVHANDLE hDevice, DRVHANDLE hDatablock, int nTagByteOffset, int nTagBitOffset, char *szCmdDataBuff, int nCmdDataLenBits)
{
	return DRV_SUCCESS;
}

 /*  初始化数据块请求计数为0 .*/
CVDRIVER_EXPORTS long OnDataBlockAdd(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
	CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
	CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDatablock);

	//初始化设置包请求失败计数为0
	CheckBlockStatus(hDevice, hDatablock, DRV_SUCCESS);

	//存储设备以及数据块配置信息
	std::map<DRVHANDLE, std::map<DRVHANDLE, CVDATABLOCK*> >::iterator it = g_mapDataBlocks.find(hDevice);
	if (it != g_mapDataBlocks.end())
	{
		it->second.insert(make_pair(hDatablock, pDataBlock));
	}
	else
	{
		std::map<DRVHANDLE, CVDATABLOCK*> mapDBs;
		mapDBs.insert(make_pair(hDatablock, pDataBlock));
		g_mapDataBlocks.insert(make_pair(hDevice, mapDBs));
	}
	
	return DRV_SUCCESS;
}

//获取版本信息  
CVDRIVER_EXPORTS long GetDrvFrameVersion()
{
    return 2;
}

CVDRIVER_EXPORTS long TagsToGroups(const TagInfo *pDevTags, int nTagsNum,
	TagInfo *pOutDevTags, unsigned int *pnTagsNum, TagGroupInfo *pTagGrps, unsigned int *pnTagGrpsNum)
{
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnDeviceAdd(DRVHANDLE hDevice)
{
	CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
    long nReconnectCnt = atoi(pDevice->pszParam2);
	if (nReconnectCnt <=0)
	{
		nReconnectCnt = 3;
	}
	ACE_Time_Value* pLastRecvDataTime = new ACE_Time_Value();
	*pLastRecvDataTime = ACE_OS::gettimeofday();
	ACE_Time_Value* pDisconnectTimeout = new ACE_Time_Value(0, nReconnectCnt*pDevice->nRecvTimeout*1000);
	Drv_SetUserDataPtr(hDevice, 0, pDisconnectTimeout);
	Drv_SetUserDataPtr(hDevice, 1, pLastRecvDataTime);

	CV_INFO(g_CVLogUdp, "OnDeviceAdd %s reconnect cnt %d ", pDevice->pszName,nReconnectCnt);
	return DRV_SUCCESS;
}
 
CVDRIVER_EXPORTS long OnDeviceDelete(DRVHANDLE hDevice)
{
	return DRV_SUCCESS;
}
 
CVDRIVER_EXPORTS long OnDataBlockDelete(DRVHANDLE hDevice, DRVHANDLE hCfgDataBlock)
{
	return DRV_SUCCESS;
}
